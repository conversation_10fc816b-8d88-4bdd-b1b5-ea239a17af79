/*
 * This file is part of EnchantmentsPlus, a bukkit plugin.
 * Copyright (c) 2015 - 2020 Zedly and Zenchantments contributors.
 * Copyright (c) 2020 - 2021 Geolykt and EnchantmentsPlus contributors
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by 
 * the Free Software Foundation, version 3.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package de.geolykt.enchantments_plus.evt;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityShootBowEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerShearEntityEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.Damageable;

import de.geolykt.enchantments_plus.Config;
import de.geolykt.enchantments_plus.CustomEnchantment;
import de.geolykt.enchantments_plus.compatibility.CompatibilityAdapter;
import de.geolykt.enchantments_plus.enums.BaseEnchantments;
import de.geolykt.enchantments_plus.util.Tool;
import de.geolykt.enchantments_plus.util.Utilities;

/**
 * Event handler for the Sturdy enchantment that prevents tools from being used when they would break.
 */
public class SturdyWatcher implements Listener {

    private static final String PERSISTENCE_MESSAGE = ChatColor.RED + "Your tool is too broken to use!";

    /**
     * Checks if a tool would break after taking damage and prevents the action if so.
     *
     * @param player The player using the tool
     * @param tool The tool being used
     * @param damageAmount The amount of damage the tool would take
     * @return true if the action should be cancelled, false otherwise
     */
    private boolean shouldPreventUsage(Player player, ItemStack tool, int damageAmount) {
        if (tool == null || tool.getType() == Material.AIR) {
            return false;
        }

        Config config = Config.get(player.getWorld());
        if (!CustomEnchantment.hasEnchantment(config, tool, BaseEnchantments.PERSISTENCE)) {
            return false;
        }



        // Check if the tool has durability
        if (!(tool.getItemMeta() instanceof Damageable)) {
            return false;
        }

        int currentDamage = CompatibilityAdapter.getDamage(tool);
        int maxDurability = tool.getType().getMaxDurability();

        // Calculate remaining durability
        int remainingDurability = maxDurability - currentDamage;

        // If the tool would break (durability would go to 0 or below), prevent usage
        if (remainingDurability <= damageAmount) {
            player.sendMessage(PERSISTENCE_MESSAGE);
            return true;
        }

        return false;
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent evt) {
        Player player = evt.getPlayer();
        ItemStack tool = Utilities.usedStack(player, true);
        
        if (shouldPreventUsage(player, tool, 1)) {
            evt.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageByEntityEvent evt) {
        if (!(evt.getDamager() instanceof Player)) {
            return;
        }
        
        Player player = (Player) evt.getDamager();
        ItemStack tool = Utilities.usedStack(player, true);
        
        if (shouldPreventUsage(player, tool, 1)) {
            evt.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onBowShoot(EntityShootBowEvent evt) {
        if (!(evt.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) evt.getEntity();
        ItemStack bow = evt.getBow();
        
        if (shouldPreventUsage(player, bow, 1)) {
            evt.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onShear(PlayerShearEntityEvent evt) {
        Player player = evt.getPlayer();
        ItemStack tool = Utilities.usedStack(player, Tool.SHEARS.contains(player.getInventory().getItemInMainHand().getType()));
        
        if (shouldPreventUsage(player, tool, 1)) {
            evt.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onPlayerInteract(PlayerInteractEvent evt) {
        Player player = evt.getPlayer();
        ItemStack tool = evt.getItem();
        
        // Only check for tools that can take damage from interaction
        if (tool != null && (Tool.HOE.contains(tool.getType()) || Tool.SHOVEL.contains(tool.getType()))) {
            if (shouldPreventUsage(player, tool, 1)) {
                evt.setCancelled(true);
            }
        }
    }
}
