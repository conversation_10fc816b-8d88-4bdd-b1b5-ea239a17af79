/*
 * This file is part of EnchantmentsPlus, a bukkit plugin.
 * Copyright (c) 2015 - 2020 Zedly and Zenchantments contributors.
 * Copyright (c) 2020 - 2021 Geolykt and EnchantmentsPlus contributors
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by 
 * the Free Software Foundation, version 3.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package de.geolykt.enchantments_plus.evt;

import org.bukkit.ChatColor;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.enchantment.EnchantItemEvent;
import org.bukkit.event.inventory.PrepareAnvilEvent;
import org.bukkit.inventory.ItemStack;

import de.geolykt.enchantments_plus.Config;
import de.geolykt.enchantments_plus.CustomEnchantment;
import de.geolykt.enchantments_plus.enums.BaseEnchantments;

/**
 * Event handler to prevent conflicting vanilla enchantments from being applied to tools with Persistence.
 */
public class SturdyConflictWatcher implements Listener {

    private static final String CONFLICT_MESSAGE = ChatColor.RED + "Cannot apply this enchantment to an item with Persistence!";

    /**
     * Checks if an item has the Persistence enchantment.
     *
     * @param item The item to check
     * @return true if the item has Persistence, false otherwise
     */
    private boolean hasSturdyEnchantment(ItemStack item) {
        if (item == null) {
            return false;
        }
        
        // We need a world context to check for custom enchantments
        // For now, we'll use a simple approach and check all possible worlds
        // In a real implementation, you might want to pass the world context
        try {
            for (org.bukkit.World world : org.bukkit.Bukkit.getWorlds()) {
                Config config = Config.get(world);
                if (CustomEnchantment.hasEnchantment(config, item, BaseEnchantments.PERSISTENCE)) {
                    return true;
                }
            }
        } catch (Exception e) {
            // If there's any issue checking, assume no Sturdy enchantment
            return false;
        }
        
        return false;
    }

    /**
     * Checks if an enchantment conflicts with Persistence.
     *
     * @param enchantment The enchantment to check
     * @return true if it conflicts with Persistence, false otherwise
     */
    private boolean conflictsWithSturdy(Enchantment enchantment) {
        return enchantment == Enchantment.MENDING || 
               enchantment == Enchantment.LOOTING || 
               enchantment == Enchantment.FORTUNE;
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onEnchantItem(EnchantItemEvent evt) {
        ItemStack item = evt.getItem();
        
        if (hasSturdyEnchantment(item)) {
            // Check if any of the enchantments being applied conflict with Persistence
            for (Enchantment enchantment : evt.getEnchantsToAdd().keySet()) {
                if (conflictsWithSturdy(enchantment)) {
                    evt.setCancelled(true);
                    evt.getEnchanter().sendMessage(CONFLICT_MESSAGE);
                    return;
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onAnvilPrepare(PrepareAnvilEvent evt) {
        if (evt.getResult() == null) {
            return;
        }
        
        ItemStack result = evt.getResult();
        ItemStack firstItem = evt.getInventory().getItem(0);
        ItemStack secondItem = evt.getInventory().getItem(1);
        
        // Check if the first item has Persistence and the second item (book or tool) has conflicting enchantments
        if (hasSturdyEnchantment(firstItem) && secondItem != null) {
            for (Enchantment enchantment : secondItem.getEnchantments().keySet()) {
                if (conflictsWithSturdy(enchantment)) {
                    evt.setResult(null); // Cancel the anvil operation
                    return;
                }
            }
        }

        // Also check if the result would have both Persistence and conflicting enchantments
        if (hasSturdyEnchantment(result)) {
            for (Enchantment enchantment : result.getEnchantments().keySet()) {
                if (conflictsWithSturdy(enchantment)) {
                    evt.setResult(null); // Cancel the anvil operation
                    return;
                }
            }
        }
    }
}
