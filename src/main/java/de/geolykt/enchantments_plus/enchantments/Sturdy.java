/*
 * This file is part of EnchantmentsPlus, a bukkit plugin.
 * Copyright (c) 2015 - 2020 Zedly and Zenchantments contributors.
 * Copyright (c) 2020 - 2021 Geolykt and EnchantmentsPlus contributors
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, version 3.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package de.geolykt.enchantments_plus.enchantments;

import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;

import de.geolykt.enchantments_plus.CustomEnchantment;
import de.geolykt.enchantments_plus.enums.BaseEnchantments;
import de.geolykt.enchantments_plus.enums.Hand;
import de.geolykt.enchantments_plus.util.Tool;

public class Sturdy extends CustomEnchantment {

    public static final int ID = 76;

    @Override
    public Builder<Sturdy> defaults() {
        return new Builder<>(Sturdy::new, ID)
            .all("Prevents tools from being used when they would break",
                    new Tool[]{Tool.SHOVEL, Tool.PICKAXE, Tool.SWORD, Tool.HOE, Tool.SHEARS, Tool.AXE, Tool.TRIDENT, Tool.BOW, Tool.CROSSBOW},
                    "Persistence",
                    1, // MAX LVL
                    Hand.BOTH);
    }

    public Sturdy() {
        super(BaseEnchantments.PERSISTENCE);
    }

    @Override
    public boolean validMaterial(ItemStack itemStack) {
        // First check if the base material is valid
        if (!super.validMaterial(itemStack)) {
            return false;
        }

        // Check for conflicting vanilla enchantments
        if (itemStack.containsEnchantment(Enchantment.MENDING) ||
            itemStack.containsEnchantment(Enchantment.LOOTING) ||
            itemStack.containsEnchantment(Enchantment.FORTUNE)) {
            return false; // Cannot apply Persistence to items with these enchantments
        }

        return true;
    }
}
